package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.diff.impl.patch.ApplyPatchStatus
import com.intellij.openapi.diff.impl.patch.PatchReader
import com.intellij.openapi.diff.impl.patch.TextFilePatch
import com.intellij.openapi.diff.impl.patch.apply.ApplyTextFilePatch
import com.intellij.openapi.diff.impl.patch.apply.GenericPatchApplier

object TextFilePatcher {
    private val logger = Logger.getInstance(TextFilePatcher::class.java)

    fun patchText(origText: String, patchText: String): Pair<String?, String?> {
        try {
            // 校验patch与原始文件的一致性
            val validationError = validatePatchConsistency(origText, patchText)
            if (validationError != null) {
                logger.warn("patch validation failed: $validationError\npatch:\n$patchText\norig:\n$origText")
                return Pair(null, validationError)
            }

            // @@里的新增代码行数和实际不一致，需要检查并修复这个计数
            val fixedPatch = fixPatchLineNumbers(patchText)

            // 检查并处理patch格式，移除前两行如果包含---和+++
            val processedPatch = preprocessPatch(fixedPatch)

            val gitDiffPath = "diff --git a/test b/test\n" +
                    "--- a/test\n" +
                    "+++ b/test\n" + processedPatch
            val patchReader = PatchReader(gitDiffPath)
            val patches = patchReader.readTextPatches()
            val patch = patches.firstOrNull()
            if (patch != null) {
                val appliedPatch = GenericPatchApplier.apply(origText, patch.hunks)
                if (appliedPatch?.status == ApplyPatchStatus.SUCCESS) {
                    return Pair(appliedPatch.patchedText, null)
                } else {
                    // 获取patch错误信息
                    try {
                        val patcher = PatchApplier(origText, patch.hunks)
                        patcher.execute()
                        logger.warn("patch apply failed\npatch:\n$processedPatch\norig:\n $origText")
                        return Pair(null, "patch apply failed")
                    } catch (e: Exception) {
                        logger.warn("patch apply failed: ${e.message}\npatch:\n$processedPatch\norig:\n $origText")
                        return Pair(null, e.message)
                    }
                }
            } else {
                logger.warn("patch parse failed\npatch:\n$processedPatch\norig:\n $origText")
                return Pair(null, "patch parse failed")
            }
        } catch (e: Exception) {
            logger.warn("fail to patch text: ${e.message}")
            return Pair(null, e.message)
        }
    }

    private fun preprocessPatch(patch: String): String {
        val lines = patch.lines()
        if (lines.size >= 2) {
            val firstLine = lines[0].trim()
            val secondLine = lines[1].trim()

            // 如果前两行包含---和+++，则移除它们
            if (firstLine.startsWith("---") && secondLine.startsWith("+++")) {
                return lines.drop(2).joinToString("\n")
            }
        }
        return patch
    }

    private fun fixPatchLineNumbers(patch: String): String {
        // 只有在全新文件的情况下才需要修复行号
        if (!isNewFilePatch(patch)) {
            return patch
        }
        
        val lines = patch.lines().toMutableList()
        var i = 0
        while (i < lines.size) {
            val line = lines[i]
            if (line.startsWith("@@")) {
                // 解析hunk头部
                val hunkMatch = Regex("@@ -([0-9]+),([0-9]+) \\+([0-9]+),([0-9]+) @@").find(line)
                if (hunkMatch != null) {
                    val (oldStart, oldCount, newStart, newCount) = hunkMatch.destructured

                    // 计算实际的新增行数
                    var actualNewCount = 0
                    var j = i + 1
                    while (j < lines.size && !lines[j].startsWith("@@")) {
                        if (lines[j].startsWith("+")) {
                            actualNewCount++
                        }
                        j++
                    }

                    // 如果实际行数与声明的不一致，修复它
                    if (actualNewCount.toString() != newCount) {
                        lines[i] = "@@ -$oldStart,$oldCount +$newStart,$actualNewCount @@"
                    }
                }
            }
            i++
        }
        return lines.joinToString("\n")
    }

    private fun isNewFilePatch(patch: String): Boolean {
        val lines = patch.lines()
        
        // 检查是否包含 /dev/null 标识（新文件的典型标志）
        val hasDevNull = lines.any { it.trim().startsWith("---") && it.contains("/dev/null") }
        
        // 检查@@行是否以-0,0开头（表示源文件为空）
        val hasZeroStart = lines.any { line ->
            line.startsWith("@@") && Regex("@@ -0,0 \\+").containsMatchIn(line)
        }
        
        return hasDevNull || hasZeroStart
    }

    private fun validatePatchConsistency(origText: String, patchText: String): String? {
        val lines = patchText.lines()
        val origLines = origText.lines()
        val origLineCount = if (origText.isEmpty()) 0 else origLines.size
        
        for (line in lines) {
            if (line.startsWith("@@")) {
                val hunkMatch = Regex("@@ -([0-9]+),([0-9]+) \\+([0-9]+),([0-9]+) @@").find(line)
                if (hunkMatch != null) {
                    val (oldStart, oldCount, newStart, newCount) = hunkMatch.destructured
                    val oldStartInt = oldStart.toInt()
                    val oldCountInt = oldCount.toInt()
                    
                    // 检查-n,0的情况：表示从第n行开始，删除0行（即原文件为空）
                    if (oldCountInt == 0) {
                        if (origLineCount > 0) {
                            return "patch expects empty file but original file has $origLineCount lines"
                        }
                    }
                    
                    // 检查其他不一致的情况
                    if (oldStartInt > 0 && oldStartInt + oldCountInt - 1 > origLineCount) {
                        return "patch references lines beyond original file length (${oldStartInt + oldCountInt - 1} > $origLineCount)"
                    }
                }
            }
        }
        
        return null
    }
}
