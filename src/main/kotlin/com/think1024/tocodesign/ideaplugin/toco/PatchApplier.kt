package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.diff.tools.util.text.LineOffsets
import com.intellij.diff.tools.util.text.LineOffsetsUtil
//import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.diff.impl.patch.PatchHunk
import com.intellij.openapi.diff.impl.patch.PatchLine
import com.intellij.openapi.util.Condition
import com.intellij.openapi.util.text.StringUtil
import com.intellij.openapi.vcs.VcsBundle
import com.intellij.util.containers.ContainerUtil
import java.util.*

class PatchApplier {
    private val myHunks: MutableList<out PatchHunk>
    private val myText: CharSequence
    private val myLineOffsets: LineOffsets

    private val sb = StringBuilder()
    private var baseLine = 0
    private var patchedLine = 0

//    constructor(myHunks: MutableList<out PatchHunk>, myText: CharSequence, myLineOffsets: LineOffsets) {
//        this.myHunks = myHunks
//        this.myText = myText
//        this.myLineOffsets = myLineOffsets
//    }

    constructor(text: CharSequence, hunks: MutableList<out PatchHunk>) {
        myText = text
        myHunks = hunks
        myLineOffsets = LineOffsetsUtil.create(text)
    }

    fun execute(): String? {
        if (myHunks.isEmpty()) return myText.toString()

        for (hunk in myHunks) {
            appendUnchangedLines(hunk.startLineBefore)

            checkContextLines(hunk)

            applyChangedLines(hunk)
        }

        if (!handleLastLine()) {
            appendUnchangedLines(myLineOffsets.lineCount)
        }

        return sb.toString()
    }

    private fun handleLastLine(): Boolean {
        val lastHunkLines = Objects.requireNonNull(ContainerUtil.getLastItem(myHunks)).lines
        val lastBaseLine = ContainerUtil.findLast<PatchLine?>(
            lastHunkLines,
            Condition { line: PatchLine? -> line!!.type != PatchLine.Type.ADD })
        val lastPatchedLine = ContainerUtil.findLast<PatchLine?>(
            lastHunkLines,
            Condition { line: PatchLine? -> line!!.type != PatchLine.Type.REMOVE })

        if (lastBaseLine != null) {
            val lastLineAlreadyApplied =
                !lastBaseLine.isSuppressNewLine && baseLine + 1 == myLineOffsets.lineCount && getLineContent(
                    baseLine
                ).isEmpty() ||
                        lastBaseLine.isSuppressNewLine && baseLine == myLineOffsets.lineCount
            if (lastLineAlreadyApplied) {
                val isNoNewlinePatched =
                    lastPatchedLine?.isSuppressNewLine ?: lastBaseLine.isSuppressNewLine
                if (!isNoNewlinePatched) {
                    if (patchedLine > 0) sb.append('\n')
                }
                return true
            }
            return false
        }

        // insertion into empty file - use "No newline at end of file" flag from patch
        if (baseLine == 0 && myText.isEmpty()) {
            val isNoNewlinePatched = lastPatchedLine != null && lastPatchedLine.isSuppressNewLine
            if (!isNoNewlinePatched) {
                if (patchedLine > 0) sb.append('\n')
            }
            return true
        }

        return false
    }

    private fun checkContextLines(hunk: PatchHunk) {
        val baseStart = hunk.startLineBefore
        val baseEnd = hunk.endLineBefore
        val patchedStart = hunk.startLineAfter
        val patchedEnd = hunk.endLineAfter

        if (baseLine != baseStart) {
            error(VcsBundle.message("patch.simple.apply.hunk.base.start.error", baseLine, baseStart))
        }
        if (patchedLine != patchedStart) {
            error(VcsBundle.message("patch.simple.apply.hunk.patched.start.error", patchedLine, patchedStart))
        }
        if (baseEnd > myLineOffsets.lineCount) {
            error(VcsBundle.message("patch.simple.apply.hunk.base.end.error", myLineOffsets.lineCount, baseEnd))
        }

        val baseCount = ContainerUtil.count<PatchLine?>(
            hunk.lines,
            Condition { patchLine: PatchLine? -> patchLine!!.type != PatchLine.Type.ADD })
        val patchedCount = ContainerUtil.count<PatchLine?>(
            hunk.lines,
            Condition { patchLine: PatchLine? -> patchLine!!.type != PatchLine.Type.REMOVE })

        if (baseCount != baseEnd - baseStart) {
            error(VcsBundle.message("patch.simple.apply.hunk.base.body.error", baseEnd - baseStart, baseCount))
        }
        if (patchedCount != patchedEnd - patchedStart) {
            error(
                VcsBundle.message(
                    "patch.simple.apply.hunk.patched.body.error",
                    patchedEnd - patchedStart,
                    patchedCount
                )
            )
        }

        var count = 0
        for (patchLine in hunk.lines) {
            if (patchLine.type != PatchLine.Type.ADD) {
                val expectedContent = getLineContent(baseStart + count)
                val actualContent = patchLine.text
                if (!StringUtil.equals(expectedContent, actualContent)) {
                    error(VcsBundle.message("patch.simple.apply.hunk.content.error", expectedContent, patchLine))
                }
                count++
            }
        }
    }


    private fun applyChangedLines(hunk: PatchHunk) {
        for (patchLine in hunk.lines) {
            if (patchLine.type != PatchLine.Type.REMOVE) {
                appendLine(patchLine.text)
            }
        }
        baseLine = hunk.endLineBefore
    }

    private fun appendUnchangedLines(untilLine: Int) {
        if (baseLine > untilLine) {
            error(VcsBundle.message("patch.simple.apply.base.line.error", baseLine, untilLine))
        }
        if (untilLine > myLineOffsets.lineCount) {
            error(
                VcsBundle.message(
                    "patch.simple.apply.base.line.total.error",
                    myLineOffsets.lineCount,
                    untilLine
                )
            )
        }

        for (i in baseLine..<untilLine) {
            appendLine(getLineContent(i))
        }
        baseLine = untilLine
    }

    private fun appendLine(lineContent: CharSequence?) {
        if (patchedLine > 0) sb.append('\n')
        sb.append(lineContent)
        patchedLine++
    }

    private fun getLineContent(line: Int): CharSequence {
        return myText.subSequence(myLineOffsets.getLineStart(line), myLineOffsets.getLineEnd(line))
    }

    companion object {
//        private val LOG = Logger.getInstance(PatchApplier::class.java)

        fun apply(text: CharSequence, hunks: MutableList<out PatchHunk>): String? {
            return PatchApplier(text, hunks).execute()
        }

        private fun error(error: String) {
            throw RuntimeException(error)
        }
    }
}